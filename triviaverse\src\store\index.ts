import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type {
  User,
  GameSession,
  TriviaWorld,
  Achievement,
  Leaderboard,
  PowerUp,
  Question,
  Answer
} from '../types';
import { mockUser, mockTriviaWorlds, mockAchievements } from '../services/mockData';

interface AppStore {
  // User state
  user: User | null;
  isAuthenticated: boolean;
  
  // Game state
  currentGame: GameSession | null;
  triviaWorlds: TriviaWorld[];
  achievements: Achievement[];
  leaderboards: Record<string, Leaderboard>;
  powerUps: PowerUp[];
  
  // UI state
  isLoading: boolean;
  error: string | null;
  currentPage: string;
  
  // Actions
  setUser: (user: User | null) => void;
  setCurrentGame: (game: GameSession | null) => void;
  updateGameSession: (updates: Partial<GameSession>) => void;
  addAnswer: (answer: Answer) => void;
  setTriviaWorlds: (worlds: TriviaWorld[]) => void;
  updateTriviaWorld: (worldId: string, updates: Partial<TriviaWorld>) => void;
  setAchievements: (achievements: Achievement[]) => void;
  unlockAchievement: (achievementId: string) => void;
  setLeaderboards: (leaderboards: Record<string, Leaderboard>) => void;
  setPowerUps: (powerUps: PowerUp[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentPage: (page: string) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  user: mockUser, // Use mock user for development
  isAuthenticated: true, // Auto-authenticate for development
  currentGame: null,
  triviaWorlds: mockTriviaWorlds,
  achievements: mockAchievements,
  leaderboards: {},
  powerUps: [],
  isLoading: false,
  error: null,
  currentPage: 'home',
};

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user 
      }),

      setCurrentGame: (game) => set({ currentGame: game }),

      updateGameSession: (updates) => set((state) => ({
        currentGame: state.currentGame 
          ? { ...state.currentGame, ...updates }
          : null
      })),

      addAnswer: (answer) => set((state) => {
        if (!state.currentGame) return state;
        
        const updatedAnswers = [...state.currentGame.answers, answer];
        const newScore = state.currentGame.score + answer.pointsEarned;
        
        return {
          currentGame: {
            ...state.currentGame,
            answers: updatedAnswers,
            score: newScore,
            currentQuestionIndex: state.currentGame.currentQuestionIndex + 1
          }
        };
      }),

      setTriviaWorlds: (worlds) => set({ triviaWorlds: worlds }),

      updateTriviaWorld: (worldId, updates) => set((state) => ({
        triviaWorlds: state.triviaWorlds.map(world =>
          world.id === worldId ? { ...world, ...updates } : world
        )
      })),

      setAchievements: (achievements) => set({ achievements }),

      unlockAchievement: (achievementId) => set((state) => ({
        achievements: state.achievements.map(achievement =>
          achievement.id === achievementId
            ? { ...achievement, isUnlocked: true, unlockedAt: new Date().toISOString() }
            : achievement
        )
      })),

      setLeaderboards: (leaderboards) => set({ leaderboards }),

      setPowerUps: (powerUps) => set({ powerUps }),

      setLoading: (loading) => set({ isLoading: loading }),

      setError: (error) => set({ error }),

      setCurrentPage: (page) => set({ currentPage: page }),

      clearError: () => set({ error: null }),

      reset: () => set(initialState),
    }),
    {
      name: 'triviaverse-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        triviaWorlds: state.triviaWorlds,
        achievements: state.achievements,
        powerUps: state.powerUps,
      }),
    }
  )
);

// Selectors
export const useUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);
export const useCurrentGame = () => useAppStore((state) => state.currentGame);
export const useTriviaWorlds = () => useAppStore((state) => state.triviaWorlds);
export const useAchievements = () => useAppStore((state) => state.achievements);
export const useLeaderboards = () => useAppStore((state) => state.leaderboards);
export const usePowerUps = () => useAppStore((state) => state.powerUps);
export const useIsLoading = () => useAppStore((state) => state.isLoading);
export const useError = () => useAppStore((state) => state.error);
export const useCurrentPage = () => useAppStore((state) => state.currentPage);

// Game-specific store for real-time game state
interface GameStore {
  timeRemaining: number;
  isPaused: boolean;
  showHint: boolean;
  usedPowerUps: string[];
  currentStreak: number;
  
  setTimeRemaining: (time: number) => void;
  setPaused: (paused: boolean) => void;
  setShowHint: (show: boolean) => void;
  addUsedPowerUp: (powerUpId: string) => void;
  setCurrentStreak: (streak: number) => void;
  resetGameState: () => void;
}

export const useGameStore = create<GameStore>((set) => ({
  timeRemaining: 0,
  isPaused: false,
  showHint: false,
  usedPowerUps: [],
  currentStreak: 0,

  setTimeRemaining: (time) => set({ timeRemaining: time }),
  setPaused: (paused) => set({ isPaused: paused }),
  setShowHint: (show) => set({ showHint: show }),
  addUsedPowerUp: (powerUpId) => set((state) => ({
    usedPowerUps: [...state.usedPowerUps, powerUpId]
  })),
  setCurrentStreak: (streak) => set({ currentStreak: streak }),
  resetGameState: () => set({
    timeRemaining: 0,
    isPaused: false,
    showHint: false,
    usedPowerUps: [],
    currentStreak: 0,
  }),
}));
