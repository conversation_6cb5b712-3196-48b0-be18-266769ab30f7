import React from 'react';
import { motion } from 'framer-motion';
import { 
  Star, 
  TrendingUp, 
  Target, 
  Zap, 
  Award,
  Plus
} from 'lucide-react';
import { formatNumber } from '../utils';

interface ScoreDisplayProps {
  currentScore: number;
  lastQuestionPoints?: number;
  streak?: number;
  accuracy?: number;
  level?: number;
  xp?: number;
  showAnimation?: boolean;
  className?: string;
}

const ScoreDisplay: React.FC<ScoreDisplayProps> = ({
  currentScore,
  lastQuestionPoints = 0,
  streak = 0,
  accuracy = 0,
  level = 1,
  xp = 0,
  showAnimation = false,
  className = ''
}) => {
  return (
    <div className={`bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6 ${className}`}>
      {/* Main Score */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse mb-2">
          <Star className="w-6 h-6 text-yellow-400" />
          <span className="text-sm text-white/70 font-arabic">النقاط الإجمالية</span>
        </div>
        
        <motion.div
          key={currentScore}
          initial={showAnimation ? { scale: 1.2, color: '#fbbf24' } : false}
          animate={{ scale: 1, color: '#ffffff' }}
          transition={{ duration: 0.5 }}
          className="text-4xl font-bold text-white mb-2"
        >
          {formatNumber(currentScore)}
        </motion.div>

        {/* Last Question Points */}
        {lastQuestionPoints > 0 && showAnimation && (
          <motion.div
            initial={{ opacity: 0, y: -20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-center space-x-1 rtl:space-x-reverse text-green-400 font-semibold"
          >
            <Plus className="w-4 h-4" />
            <span>{formatNumber(lastQuestionPoints)}</span>
          </motion.div>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        {/* Streak */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse mb-1">
            <Zap className="w-4 h-4 text-orange-400" />
            <span className="text-xs text-white/70 font-arabic">السلسلة</span>
          </div>
          <motion.div
            key={streak}
            initial={showAnimation && streak > 0 ? { scale: 1.3 } : false}
            animate={{ scale: 1 }}
            transition={{ duration: 0.3 }}
            className="text-xl font-bold text-orange-400"
          >
            {streak}
          </motion.div>
        </div>

        {/* Accuracy */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse mb-1">
            <Target className="w-4 h-4 text-green-400" />
            <span className="text-xs text-white/70 font-arabic">الدقة</span>
          </div>
          <div className="text-xl font-bold text-green-400">
            {accuracy}%
          </div>
        </div>

        {/* Level */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse mb-1">
            <Award className="w-4 h-4 text-purple-400" />
            <span className="text-xs text-white/70 font-arabic">المستوى</span>
          </div>
          <div className="text-xl font-bold text-purple-400">
            {level}
          </div>
        </div>

        {/* XP */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse mb-1">
            <TrendingUp className="w-4 h-4 text-blue-400" />
            <span className="text-xs text-white/70 font-arabic">الخبرة</span>
          </div>
          <div className="text-xl font-bold text-blue-400">
            {formatNumber(xp)}
          </div>
        </div>
      </div>

      {/* XP Progress Bar */}
      <div className="mt-4">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-white/60 font-arabic">التقدم للمستوى التالي</span>
          <span className="text-xs text-white/60">
            {formatNumber(xp % 1000)} / 1000
          </span>
        </div>
        <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-blue-400 to-purple-500"
            initial={{ width: 0 }}
            animate={{ width: `${((xp % 1000) / 1000) * 100}%` }}
            transition={{ duration: 1, delay: 0.5 }}
          />
        </div>
      </div>
    </div>
  );
};

export default ScoreDisplay;
