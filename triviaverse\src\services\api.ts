import axios from 'axios';
import type { 
  User, 
  Question, 
  GameSession, 
  TriviaWorld, 
  Achievement, 
  Leaderboard,
  ApiResponse,
  PaginatedResponse,
  QuestionCategory
} from '../types';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
const TRIVIA_API_URL = 'https://opentdb.com/api.php';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth-token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email: string, password: string): Promise<ApiResponse<{ user: User; token: string }>> => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  register: async (userData: {
    username: string;
    email: string;
    password: string;
  }): Promise<ApiResponse<{ user: User; token: string }>> => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  logout: async (): Promise<ApiResponse<null>> => {
    const response = await api.post('/auth/logout');
    localStorage.removeItem('auth-token');
    return response.data;
  },

  refreshToken: async (): Promise<ApiResponse<{ token: string }>> => {
    const response = await api.post('/auth/refresh');
    return response.data;
  },

  getProfile: async (): Promise<ApiResponse<User>> => {
    const response = await api.get('/auth/profile');
    return response.data;
  },

  updateProfile: async (updates: Partial<User>): Promise<ApiResponse<User>> => {
    const response = await api.patch('/auth/profile', updates);
    return response.data;
  },
};

// Questions API
export const questionsAPI = {
  getQuestions: async (params: {
    category?: QuestionCategory;
    difficulty?: 'easy' | 'medium' | 'hard';
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<PaginatedResponse<Question>>> => {
    const response = await api.get('/questions', { params });
    return response.data;
  },

  getQuestionById: async (id: string): Promise<ApiResponse<Question>> => {
    const response = await api.get(`/questions/${id}`);
    return response.data;
  },

  getDailyChallenge: async (): Promise<ApiResponse<Question>> => {
    const response = await api.get('/questions/daily-challenge');
    return response.data;
  },

  getDailyRiddle: async (): Promise<ApiResponse<Question>> => {
    const response = await api.get('/questions/daily-riddle');
    return response.data;
  },

  // External API for additional questions
  getExternalQuestions: async (params: {
    amount: number;
    category?: number;
    difficulty?: 'easy' | 'medium' | 'hard';
    type?: 'multiple' | 'boolean';
  }): Promise<any> => {
    const response = await axios.get(TRIVIA_API_URL, { params });
    return response.data;
  },

  submitQuestion: async (question: Omit<Question, 'id' | 'createdAt'>): Promise<ApiResponse<Question>> => {
    const response = await api.post('/questions', question);
    return response.data;
  },

  rateQuestion: async (questionId: string, rating: number): Promise<ApiResponse<null>> => {
    const response = await api.post(`/questions/${questionId}/rate`, { rating });
    return response.data;
  },
};

// Game API
export const gameAPI = {
  startGame: async (gameData: {
    mode: string;
    category?: QuestionCategory;
    difficulty?: 'easy' | 'medium' | 'hard';
    questionCount?: number;
  }): Promise<ApiResponse<GameSession>> => {
    const response = await api.post('/games/start', gameData);
    return response.data;
  },

  submitAnswer: async (gameId: string, answer: {
    questionId: string;
    selectedAnswer: number;
    timeSpent: number;
  }): Promise<ApiResponse<{ isCorrect: boolean; pointsEarned: number; explanation?: string }>> => {
    const response = await api.post(`/games/${gameId}/answer`, answer);
    return response.data;
  },

  endGame: async (gameId: string): Promise<ApiResponse<{
    finalScore: number;
    xpEarned: number;
    achievementsUnlocked: Achievement[];
    newLevel?: number;
  }>> => {
    const response = await api.post(`/games/${gameId}/end`);
    return response.data;
  },

  getGameHistory: async (params: {
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<PaginatedResponse<GameSession>>> => {
    const response = await api.get('/games/history', { params });
    return response.data;
  },

  getGameStats: async (): Promise<ApiResponse<{
    totalGames: number;
    totalScore: number;
    averageScore: number;
    accuracy: number;
    favoriteCategory: QuestionCategory;
  }>> => {
    const response = await api.get('/games/stats');
    return response.data;
  },
};

// Trivia Worlds API
export const worldsAPI = {
  getWorlds: async (): Promise<ApiResponse<TriviaWorld[]>> => {
    const response = await api.get('/worlds');
    return response.data;
  },

  getWorldProgress: async (worldId: string): Promise<ApiResponse<{
    completedQuestions: number;
    totalQuestions: number;
    accuracy: number;
    isUnlocked: boolean;
  }>> => {
    const response = await api.get(`/worlds/${worldId}/progress`);
    return response.data;
  },

  unlockWorld: async (worldId: string): Promise<ApiResponse<TriviaWorld>> => {
    const response = await api.post(`/worlds/${worldId}/unlock`);
    return response.data;
  },
};

// Achievements API
export const achievementsAPI = {
  getAchievements: async (): Promise<ApiResponse<Achievement[]>> => {
    const response = await api.get('/achievements');
    return response.data;
  },

  checkAchievements: async (): Promise<ApiResponse<Achievement[]>> => {
    const response = await api.post('/achievements/check');
    return response.data;
  },
};

// Leaderboards API
export const leaderboardsAPI = {
  getLeaderboard: async (type: 'daily' | 'weekly' | 'monthly' | 'all-time', params?: {
    limit?: number;
    category?: QuestionCategory;
  }): Promise<ApiResponse<Leaderboard>> => {
    const response = await api.get(`/leaderboards/${type}`, { params });
    return response.data;
  },

  getUserRank: async (type: 'daily' | 'weekly' | 'monthly' | 'all-time'): Promise<ApiResponse<{
    rank: number;
    score: number;
    total: number;
  }>> => {
    const response = await api.get(`/leaderboards/${type}/rank`);
    return response.data;
  },
};

// Multiplayer API
export const multiplayerAPI = {
  createRoom: async (settings: {
    maxPlayers: number;
    questionCount: number;
    timePerQuestion: number;
    difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
    categories: QuestionCategory[];
  }): Promise<ApiResponse<{ roomId: string; joinCode: string }>> => {
    const response = await api.post('/multiplayer/rooms', settings);
    return response.data;
  },

  joinRoom: async (roomCode: string): Promise<ApiResponse<{ roomId: string }>> => {
    const response = await api.post('/multiplayer/join', { roomCode });
    return response.data;
  },

  leaveRoom: async (roomId: string): Promise<ApiResponse<null>> => {
    const response = await api.post(`/multiplayer/rooms/${roomId}/leave`);
    return response.data;
  },

  getRoomInfo: async (roomId: string): Promise<ApiResponse<any>> => {
    const response = await api.get(`/multiplayer/rooms/${roomId}`);
    return response.data;
  },
};

// Power-ups API
export const powerUpsAPI = {
  getPowerUps: async (): Promise<ApiResponse<any[]>> => {
    const response = await api.get('/powerups');
    return response.data;
  },

  usePowerUp: async (gameId: string, powerUpId: string): Promise<ApiResponse<{
    effect: any;
    remainingUses: number;
  }>> => {
    const response = await api.post(`/games/${gameId}/powerups/${powerUpId}`);
    return response.data;
  },

  purchasePowerUp: async (powerUpId: string, quantity: number): Promise<ApiResponse<{
    newBalance: number;
    totalOwned: number;
  }>> => {
    const response = await api.post('/powerups/purchase', { powerUpId, quantity });
    return response.data;
  },
};

export default api;
