import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

// Utility function for combining Tailwind classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Scoring algorithm implementation
export function calculateScore(
  basePoints: number,
  timeSpent: number,
  timeLimit: number,
  attempts: number
): number {
  // Base points based on difficulty
  let score = basePoints;

  // Time multiplier (1.5x if answered within 10 seconds)
  if (timeSpent <= 10) {
    score *= 1.5;
  }

  // Attempt penalty
  if (attempts === 1) {
    // Full points for first attempt
    score *= 1;
  } else if (attempts === 2) {
    // -25% for second attempt
    score *= 0.75;
  } else if (attempts === 3) {
    // -50% for third attempt
    score *= 0.5;
  } else {
    // No points for more than 3 attempts
    score = 0;
  }

  return Math.round(score);
}

// XP calculation based on performance
export function calculateXP(score: number, difficulty: 'easy' | 'medium' | 'hard'): number {
  const difficultyMultiplier = {
    easy: 1,
    medium: 1.5,
    hard: 2
  };

  return Math.round(score * 0.1 * difficultyMultiplier[difficulty]);
}

// Level calculation from XP
export function calculateLevel(xp: number): number {
  return Math.floor(xp / 1000) + 1;
}

// XP required for next level
export function getXPForNextLevel(currentLevel: number): number {
  return currentLevel * 1000;
}

// Format time in Arabic
export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  return `${remainingSeconds}`;
}

// Format numbers in Arabic
export function formatNumber(num: number): string {
  return new Intl.NumberFormat('ar-EG').format(num);
}

// Format date in Arabic
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(dateObj);
}

// Shuffle array (Fisher-Yates algorithm)
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Generate random ID
export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

// Validate email
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate Arabic text
export function isArabicText(text: string): boolean {
  const arabicRegex = /[\u0600-\u06FF]/;
  return arabicRegex.test(text);
}

// Get difficulty color
export function getDifficultyColor(difficulty: 'easy' | 'medium' | 'hard'): string {
  switch (difficulty) {
    case 'easy':
      return 'text-green-400';
    case 'medium':
      return 'text-yellow-400';
    case 'hard':
      return 'text-red-400';
    default:
      return 'text-gray-400';
  }
}

// Get category color
export function getCategoryColor(category: string): string {
  const colors = {
    history: 'from-amber-500 to-orange-600',
    science: 'from-blue-500 to-cyan-600',
    culture: 'from-purple-500 to-pink-600',
    movies: 'from-red-500 to-rose-600',
    technology: 'from-green-500 to-emerald-600',
    mystery: 'from-indigo-500 to-violet-600'
  };
  return colors[category as keyof typeof colors] || 'from-gray-500 to-gray-600';
}

// Calculate accuracy percentage
export function calculateAccuracy(correct: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((correct / total) * 100);
}

// Get achievement rarity color
export function getRarityColor(rarity: 'common' | 'rare' | 'epic' | 'legendary'): string {
  switch (rarity) {
    case 'common':
      return 'text-gray-400';
    case 'rare':
      return 'text-blue-400';
    case 'epic':
      return 'text-purple-400';
    case 'legendary':
      return 'text-yellow-400';
    default:
      return 'text-gray-400';
  }
}

// Debounce function
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Local storage helpers
export const storage = {
  get: <T>(key: string): T | null => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch {
      return null;
    }
  },
  set: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch {
      // Handle storage errors silently
    }
  },
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key);
    } catch {
      // Handle storage errors silently
    }
  },
  clear: (): void => {
    try {
      localStorage.clear();
    } catch {
      // Handle storage errors silently
    }
  }
};

// Constants
export const GAME_CONSTANTS = {
  POINTS: {
    EASY: 10,
    MEDIUM: 20,
    HARD: 30
  },
  TIME_LIMITS: {
    EASY: 30,
    MEDIUM: 45,
    HARD: 60
  },
  XP_PER_LEVEL: 1000,
  REQUIRED_ACCURACY: 0.7,
  TIME_BONUS_THRESHOLD: 10,
  TIME_BONUS_MULTIPLIER: 1.5
};

// Arabic translations
export const TRANSLATIONS = {
  DIFFICULTIES: {
    easy: 'سهل',
    medium: 'متوسط',
    hard: 'صعب'
  },
  CATEGORIES: {
    history: 'التاريخ',
    science: 'العلوم',
    culture: 'الثقافة',
    movies: 'السينما',
    technology: 'التكنولوجيا',
    mystery: 'الغموض'
  },
  GAME_MODES: {
    'daily-challenge': 'تحدي اليوم',
    'trivia-worlds': 'عوالم الأسئلة',
    'time-rush': 'سباق الزمن',
    'friend-challenge': 'تحدي الأصدقاء',
    'daily-riddle': 'لغز اليوم',
    'battle-royale': 'المعركة الملكية'
  }
};
