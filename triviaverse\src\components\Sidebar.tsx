import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Home,
  Globe,
  Calendar,
  Clock,
  Users,
  HelpCircle,
  Trophy,
  Zap,
  Target
} from 'lucide-react';
import { cn } from '../utils';

const Sidebar: React.FC = () => {
  const location = useLocation();

  const menuItems = [
    {
      path: '/dashboard',
      icon: Home,
      label: 'الرئيسية',
      description: 'لوحة التحكم'
    },
    {
      path: '/worlds',
      icon: Globe,
      label: 'عوالم الأسئلة',
      description: 'استكشف المجرات'
    },
    {
      path: '/daily-challenge',
      icon: Calendar,
      label: 'تحدي اليوم',
      description: 'سؤال يومي جديد'
    },
    {
      path: '/time-rush',
      icon: Clock,
      label: 'سباق الزمن',
      description: '60 ثانية من الإثارة'
    },
    {
      path: '/friend-challenge',
      icon: Users,
      label: 'تحدي الأصدقاء',
      description: 'العب مع الآخرين'
    },
    {
      path: '/daily-riddle',
      icon: HelpCircle,
      label: 'لغز اليوم',
      description: 'تحدي عقلك'
    },
    {
      path: '/leaderboard',
      icon: Trophy,
      label: 'لوحة المتصدرين',
      description: 'تنافس مع الأفضل'
    }
  ];

  return (
    <motion.aside
      initial={{ x: 100 }}
      animate={{ x: 0 }}
      className="fixed left-0 top-20 h-[calc(100vh-5rem)] w-64 bg-black/20 backdrop-blur-md border-r border-white/10 p-6 overflow-y-auto"
    >
      <nav className="space-y-2">
        {menuItems.map((item, index) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;

          return (
            <motion.div
              key={item.path}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Link
                to={item.path}
                className={cn(
                  'group flex items-center space-x-3 rtl:space-x-reverse p-3 rounded-lg transition-all duration-200',
                  isActive
                    ? 'bg-primary-500/20 text-primary-300 border border-primary-500/30'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                )}
              >
                <div className={cn(
                  'p-2 rounded-lg transition-colors',
                  isActive
                    ? 'bg-primary-500/30'
                    : 'bg-white/10 group-hover:bg-white/20'
                )}>
                  <Icon className="w-5 h-5" />
                </div>
                
                <div className="flex-1">
                  <p className={cn(
                    'font-semibold text-sm',
                    isActive ? 'text-primary-300' : 'text-white'
                  )}>
                    {item.label}
                  </p>
                  <p className="text-xs text-white/50 mt-0.5">
                    {item.description}
                  </p>
                </div>

                {isActive && (
                  <motion.div
                    layoutId="activeIndicator"
                    className="w-1 h-8 bg-primary-400 rounded-full"
                  />
                )}
              </Link>
            </motion.div>
          );
        })}
      </nav>

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="mt-8 p-4 bg-gradient-to-br from-secondary-500/20 to-accent-500/20 rounded-lg border border-white/10"
      >
        <h3 className="text-sm font-semibold text-white mb-3">إحصائياتك السريعة</h3>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Target className="w-4 h-4 text-green-400" />
              <span className="text-xs text-white/70">دقة الإجابات</span>
            </div>
            <span className="text-sm font-bold text-green-400">85%</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Zap className="w-4 h-4 text-yellow-400" />
              <span className="text-xs text-white/70">سلسلة الانتصارات</span>
            </div>
            <span className="text-sm font-bold text-yellow-400">7 أيام</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Trophy className="w-4 h-4 text-purple-400" />
              <span className="text-xs text-white/70">الترتيب</span>
            </div>
            <span className="text-sm font-bold text-purple-400">#42</span>
          </div>
        </div>
      </motion.div>

      {/* Daily Challenge Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
        className="mt-4 p-4 bg-gradient-to-br from-primary-500/20 to-blue-500/20 rounded-lg border border-white/10"
      >
        <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
          <Calendar className="w-4 h-4 text-primary-400" />
          <h3 className="text-sm font-semibold text-white">تحدي اليوم</h3>
        </div>
        
        <p className="text-xs text-white/70 mb-3">
          سؤال جديد في انتظارك! هل أنت مستعد للتحدي؟
        </p>
        
        <Link
          to="/daily-challenge"
          className="block w-full py-2 px-3 bg-primary-500 hover:bg-primary-600 text-white text-xs font-semibold rounded-lg text-center transition-colors"
        >
          ابدأ التحدي
        </Link>
      </motion.div>
    </motion.aside>
  );
};

export default Sidebar;
