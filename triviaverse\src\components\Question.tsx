import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Clock,
  HelpCircle,
  CheckCircle,
  XCircle,
  Lightbulb,
  Zap
} from 'lucide-react';
import type { Question as QuestionType, Answer } from '../types';
import { cn, getDifficultyColor, GAME_CONSTANTS } from '../utils';

interface QuestionProps {
  question: QuestionType;
  questionNumber: number;
  totalQuestions: number;
  timeRemaining: number;
  onAnswer: (answer: Answer) => void;
  onTimeUp: () => void;
  showHint?: boolean;
  isAnswered?: boolean;
  selectedAnswer?: number;
  correctAnswer?: number;
  showExplanation?: boolean;
}

const Question: React.FC<QuestionProps> = ({
  question,
  questionNumber,
  totalQuestions,
  timeRemaining,
  onAnswer,
  onTimeUp,
  showHint = false,
  isAnswered = false,
  selectedAnswer,
  correctAnswer,
  showExplanation = false
}) => {
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [attempts, setAttempts] = useState(0);
  const [startTime] = useState(Date.now());

  useEffect(() => {
    if (timeRemaining <= 0 && !isAnswered) {
      onTimeUp();
    }
  }, [timeRemaining, isAnswered, onTimeUp]);

  const handleOptionSelect = (optionIndex: number) => {
    if (isAnswered) return;

    setSelectedOption(optionIndex);
    setAttempts(prev => prev + 1);

    const timeSpent = Math.floor((Date.now() - startTime) / 1000);
    const isCorrect = optionIndex === question.correctAnswer;
    
    // Calculate points based on scoring algorithm
    const basePoints = GAME_CONSTANTS.POINTS[question.difficulty.toUpperCase() as keyof typeof GAME_CONSTANTS.POINTS];
    let pointsEarned = basePoints;

    // Time multiplier
    if (timeSpent <= GAME_CONSTANTS.TIME_BONUS_THRESHOLD) {
      pointsEarned *= GAME_CONSTANTS.TIME_BONUS_MULTIPLIER;
    }

    // Attempt penalty
    if (attempts === 0) {
      pointsEarned *= 1; // Full points
    } else if (attempts === 1) {
      pointsEarned *= 0.75; // -25%
    } else if (attempts === 2) {
      pointsEarned *= 0.5; // -50%
    } else {
      pointsEarned = 0; // No points
    }

    if (!isCorrect) {
      pointsEarned = 0;
    }

    const answer: Answer = {
      questionId: question.id,
      selectedAnswer: optionIndex,
      isCorrect,
      timeSpent,
      attempts: attempts + 1,
      pointsEarned: Math.round(pointsEarned),
      timestamp: new Date().toISOString()
    };

    onAnswer(answer);
  };

  const getTimeColor = () => {
    const percentage = (timeRemaining / question.timeLimit) * 100;
    if (percentage > 50) return 'text-green-400';
    if (percentage > 25) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getOptionStatus = (optionIndex: number) => {
    if (!isAnswered) return 'default';
    if (optionIndex === question.correctAnswer) return 'correct';
    if (optionIndex === selectedAnswer && optionIndex !== question.correctAnswer) return 'incorrect';
    return 'default';
  };

  const getOptionStyles = (optionIndex: number) => {
    const status = getOptionStatus(optionIndex);
    
    switch (status) {
      case 'correct':
        return 'bg-green-500/20 border-green-500 text-green-300';
      case 'incorrect':
        return 'bg-red-500/20 border-red-500 text-red-300';
      default:
        return 'bg-white/5 border-white/20 text-white hover:bg-white/10 hover:border-white/30';
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Question Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between mb-8"
      >
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <span className="text-white/70 font-arabic">السؤال</span>
            <span className="text-2xl font-bold text-white">
              {questionNumber}
            </span>
            <span className="text-white/70 font-arabic">من</span>
            <span className="text-xl font-bold text-white">
              {totalQuestions}
            </span>
          </div>
          
          <div className={`px-3 py-1 rounded-full text-xs font-semibold ${getDifficultyColor(question.difficulty)} bg-white/10`}>
            {question.difficulty === 'easy' ? 'سهل' : question.difficulty === 'medium' ? 'متوسط' : 'صعب'}
          </div>
        </div>

        {/* Timer */}
        <motion.div
          animate={{ scale: timeRemaining <= 10 ? [1, 1.1, 1] : 1 }}
          transition={{ duration: 0.5, repeat: timeRemaining <= 10 ? Infinity : 0 }}
          className={`flex items-center space-x-2 rtl:space-x-reverse ${getTimeColor()}`}
        >
          <Clock className="w-5 h-5" />
          <span className="text-xl font-bold">
            {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}
          </span>
        </motion.div>
      </motion.div>

      {/* Progress Bar */}
      <div className="w-full h-2 bg-white/20 rounded-full mb-8 overflow-hidden">
        <motion.div
          className="h-full bg-gradient-to-r from-primary-400 to-secondary-400"
          initial={{ width: 0 }}
          animate={{ width: `${(questionNumber / totalQuestions) * 100}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>

      {/* Question Card */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 mb-8"
      >
        {/* Question Text */}
        <div className="flex items-start space-x-4 rtl:space-x-reverse mb-8">
          <div className="p-3 bg-primary-500/20 rounded-lg">
            <HelpCircle className="w-6 h-6 text-primary-400" />
          </div>
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-white leading-relaxed font-arabic">
              {question.text}
            </h2>
            {showHint && question.explanation && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="mt-4 p-4 bg-yellow-500/20 border border-yellow-500/30 rounded-lg"
              >
                <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                  <Lightbulb className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm font-semibold text-yellow-400 font-arabic">تلميح</span>
                </div>
                <p className="text-sm text-white/80 font-arabic">{question.explanation}</p>
              </motion.div>
            )}
          </div>
        </div>

        {/* Answer Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {question.options.map((option, index) => {
            const status = getOptionStatus(index);
            const Icon = status === 'correct' ? CheckCircle : status === 'incorrect' ? XCircle : null;
            
            return (
              <motion.button
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 + index * 0.1 }}
                whileHover={!isAnswered ? { scale: 1.02 } : {}}
                whileTap={!isAnswered ? { scale: 0.98 } : {}}
                onClick={() => handleOptionSelect(index)}
                disabled={isAnswered}
                className={cn(
                  'p-4 rounded-xl border-2 transition-all duration-300 text-right',
                  'flex items-center justify-between',
                  getOptionStyles(index),
                  isAnswered && 'cursor-not-allowed'
                )}
              >
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className={cn(
                    'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                    status === 'correct' ? 'bg-green-500' : 
                    status === 'incorrect' ? 'bg-red-500' : 'bg-white/20'
                  )}>
                    {String.fromCharCode(65 + index)}
                  </div>
                  <span className="font-arabic text-lg">{option}</span>
                </div>
                
                {Icon && <Icon className="w-5 h-5" />}
              </motion.button>
            );
          })}
        </div>

        {/* Explanation */}
        <AnimatePresence>
          {showExplanation && question.explanation && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-6 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg"
            >
              <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
                <Zap className="w-4 h-4 text-blue-400" />
                <span className="text-sm font-semibold text-blue-400 font-arabic">التفسير</span>
              </div>
              <p className="text-white/80 font-arabic leading-relaxed">{question.explanation}</p>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default Question;
