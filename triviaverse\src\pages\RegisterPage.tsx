import React from 'react';
import { motion } from 'framer-motion';

const RegisterPage: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-gradient-to-br from-nebula-dark via-nebula-purple to-nebula-blue flex items-center justify-center p-6"
    >
      <div className="text-center text-white">
        <h1 className="text-4xl font-bold mb-4 font-arabic">صفحة التسجيل</h1>
        <p className="text-white/70 font-arabic">قريباً...</p>
      </div>
    </motion.div>
  );
};

export default RegisterPage;
