import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calendar,
  Star,
  Clock,
  Trophy,
  Users,
  Target,
  Play,
  CheckCircle,
  Gift,
  Flame
} from 'lucide-react';
import { useAppStore, useGameStore } from '../store';
import { questionsAPI, gameAPI } from '../services/api';
import Question from '../components/Question';
import GameHeader from '../components/GameHeader';
import ScoreDisplay from '../components/ScoreDisplay';
import LoadingSpinner from '../components/LoadingSpinner';
import type { Question as QuestionType, Answer, GameSession } from '../types';
import { calculateScore, calculateXP, formatNumber } from '../utils';

const DailyChallengeePage: React.FC = () => {
  const navigate = useNavigate();
  const { user, setCurrentGame, addAnswer } = useAppStore();
  const {
    timeRemaining,
    isPaused,
    setTimeRemaining,
    setPaused,
    resetGameState
  } = useGameStore();

  const [gameState, setGameState] = useState<'loading' | 'intro' | 'playing' | 'completed'>('loading');
  const [question, setQuestion] = useState<QuestionType | null>(null);
  const [currentAnswer, setCurrentAnswer] = useState<Answer | null>(null);
  const [gameSession, setGameSession] = useState<GameSession | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Daily challenge stats
  const [dailyStats] = useState({
    participantsToday: 1247,
    averageScore: 85,
    topScore: 200,
    timeLeft: '18:42:15'
  });

  useEffect(() => {
    loadDailyChallenge();
    return () => resetGameState();
  }, []);

  const loadDailyChallenge = async () => {
    try {
      setGameState('loading');
      const response = await questionsAPI.getDailyChallenge();

      if (response.success && response.data) {
        setQuestion(response.data);
        setTimeRemaining(response.data.timeLimit);
        setGameState('intro');
      } else {
        setError('فشل في تحميل تحدي اليوم');
      }
    } catch (error) {
      console.error('Error loading daily challenge:', error);
      setError('حدث خطأ في تحميل التحدي');
    }
  };

  const startChallenge = async () => {
    if (!question) return;

    try {
      const response = await gameAPI.startGame({
        mode: 'daily-challenge',
        questionCount: 1
      });

      if (response.success && response.data) {
        setGameSession(response.data);
        setCurrentGame(response.data);
        setGameState('playing');
      }
    } catch (error) {
      console.error('Error starting challenge:', error);
      setError('فشل في بدء التحدي');
    }
  };

  const handleAnswer = async (answer: Answer) => {
    if (!gameSession || !question) return;

    setCurrentAnswer(answer);
    addAnswer(answer);
    setShowResult(true);

    try {
      await gameAPI.submitAnswer(gameSession.id, {
        questionId: answer.questionId,
        selectedAnswer: answer.selectedAnswer,
        timeSpent: answer.timeSpent
      });

      // Wait for result display, then complete the game
      setTimeout(async () => {
        await completeChallenge();
      }, 3000);
    } catch (error) {
      console.error('Error submitting answer:', error);
    }
  };

  const completeChallenge = async () => {
    if (!gameSession) return;

    try {
      const response = await gameAPI.endGame(gameSession.id);
      if (response.success) {
        setGameState('completed');
      }
    } catch (error) {
      console.error('Error completing challenge:', error);
    }
  };

  const handleTimeUp = () => {
    if (!question) return;

    const answer: Answer = {
      questionId: question.id,
      selectedAnswer: -1, // No answer selected
      isCorrect: false,
      timeSpent: question.timeLimit,
      attempts: 1,
      pointsEarned: 0,
      timestamp: new Date().toISOString()
    };

    handleAnswer(answer);
  };

  const handlePause = () => setPaused(true);
  const handleResume = () => setPaused(false);
  const handleSoundToggle = () => setSoundEnabled(!soundEnabled);
  const handleExit = () => navigate('/dashboard');

  if (gameState === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-nebula-dark via-nebula-purple to-nebula-blue flex items-center justify-center">
        <LoadingSpinner size="large" text="جاري تحميل تحدي اليوم..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-nebula-dark via-nebula-purple to-nebula-blue flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 text-center max-w-md"
        >
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Calendar className="w-8 h-8 text-red-400" />
          </div>
          <h2 className="text-2xl font-bold text-white mb-4 font-arabic">خطأ في التحدي</h2>
          <p className="text-white/70 mb-6 font-arabic">{error}</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white font-bold rounded-lg transition-colors font-arabic"
          >
            العودة للرئيسية
          </button>
        </motion.div>
      </div>
    );
  }

  // Intro Screen
  if (gameState === 'intro') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-nebula-dark via-nebula-purple to-nebula-blue">
        <div className="max-w-4xl mx-auto px-6 py-12">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <Calendar className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white mb-4 font-arabic">تحدي اليوم</h1>
            <p className="text-xl text-white/70 font-arabic">اختبر معلوماتك في سؤال اليوم الخاص</p>
          </motion.div>

          {/* Start Button */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6 }}
            className="text-center"
          >
            <button
              onClick={startChallenge}
              className="group px-12 py-4 bg-gradient-to-r from-orange-500 to-red-600 text-white font-bold rounded-xl text-xl hover:from-orange-600 hover:to-red-700 transition-all duration-300 transform hover:scale-105 font-arabic"
            >
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Play className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
                <span>ابدأ التحدي</span>
              </div>
            </button>
          </motion.div>
        </div>
      </div>
    );
  }

  // Playing State
  if (gameState === 'playing' && question) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-nebula-dark via-nebula-purple to-nebula-blue">
        <GameHeader
          title="تحدي اليوم"
          subtitle="سؤال واحد، فرصة واحدة"
          timeRemaining={timeRemaining}
          totalTime={question.timeLimit}
          isPaused={isPaused}
          isSoundEnabled={soundEnabled}
          onPause={handlePause}
          onResume={handleResume}
          onTimeUp={handleTimeUp}
          onSoundToggle={handleSoundToggle}
          onExit={handleExit}
        />

        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Question Area */}
            <div className="lg:col-span-3">
              <Question
                question={question}
                questionNumber={1}
                totalQuestions={1}
                timeRemaining={timeRemaining}
                onAnswer={handleAnswer}
                onTimeUp={handleTimeUp}
                isAnswered={showResult}
                selectedAnswer={currentAnswer?.selectedAnswer}
                correctAnswer={showResult ? question.correctAnswer : undefined}
                showExplanation={showResult}
              />
            </div>

            {/* Score Sidebar */}
            <div className="lg:col-span-1">
              <ScoreDisplay
                currentScore={gameSession?.score || 0}
                lastQuestionPoints={currentAnswer?.pointsEarned}
                streak={user?.streakDays || 0}
                accuracy={85}
                level={user?.level || 1}
                xp={user?.xp || 0}
                showAnimation={showResult}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Completed State
  if (gameState === 'completed') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-nebula-dark via-nebula-purple to-nebula-blue flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 text-center max-w-md"
        >
          <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-10 h-10 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-white mb-4 font-arabic">تم إكمال التحدي!</h2>
          <p className="text-white/70 mb-6 font-arabic">
            حصلت على {currentAnswer?.pointsEarned || 0} نقطة
          </p>
          <button
            onClick={() => navigate('/dashboard')}
            className="px-8 py-3 bg-primary-500 hover:bg-primary-600 text-white font-bold rounded-lg transition-colors font-arabic"
          >
            العودة للرئيسية
          </button>
        </motion.div>
      </div>
    );
  }

  return null;
};

export default DailyChallengeePage;
