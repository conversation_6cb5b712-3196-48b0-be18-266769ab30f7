import React from 'react';
import { motion } from 'framer-motion';

const DailyChallengeePage: React.FC = () => {
  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="p-6">
      <div className="text-center text-white">
        <h1 className="text-4xl font-bold mb-4 font-arabic">تحدي اليوم</h1>
        <p className="text-white/70 font-arabic">قريباً...</p>
      </div>
    </motion.div>
  );
};

export default DailyChallengeePage;
