import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAppStore } from '../store';
import LoadingSpinner from './LoadingSpinner';

const ProtectedRoute: React.FC = () => {
  const { isAuthenticated, isLoading } = useAppStore();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-nebula-dark via-nebula-purple to-nebula-blue flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return isAuthenticated ? <Outlet /> : <Navigate to="/login" replace />;
};

export default ProtectedRoute;
