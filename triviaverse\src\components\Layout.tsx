import React from 'react';
import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import { useAppStore } from '../store';

const Layout: React.FC = () => {
  const { currentPage } = useAppStore();

  return (
    <div className="min-h-screen bg-gradient-to-br from-nebula-dark via-nebula-purple to-nebula-blue">
      <Navbar />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6 mr-64">
          <motion.div
            key={currentPage}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="max-w-7xl mx-auto"
          >
            <Outlet />
          </motion.div>
        </main>
      </div>
    </div>
  );
};

export default Layout;
