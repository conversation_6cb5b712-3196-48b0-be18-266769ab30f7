import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Calendar,
  Clock,
  Users,
  HelpCircle,
  Trophy,
  Globe,
  Star,
  Zap,
  Target,
  TrendingUp,
  Award,
  Play,
  ChevronLeft,
  Fire
} from 'lucide-react';
import { useAppStore } from '../store';
import { formatNumber, calculateLevel, getXPForNextLevel } from '../utils';

const DashboardPage: React.FC = () => {
  const { user } = useAppStore();

  if (!user) return null;

  const currentLevel = calculateLevel(user.xp);
  const xpForNextLevel = getXPForNextLevel(currentLevel);
  const xpProgress = ((user.xp % 1000) / 1000) * 100;

  const gameModesCards = [
    {
      id: 'daily-challenge',
      title: 'تحدي اليوم',
      description: 'سؤال جديد كل يوم',
      icon: Calendar,
      color: 'from-orange-500 to-red-600',
      path: '/daily-challenge',
      status: 'متاح الآن',
      statusColor: 'text-green-400'
    },
    {
      id: 'trivia-worlds',
      title: 'عوالم الأسئلة',
      description: 'استكشف 6 عوالم مختلفة',
      icon: Globe,
      color: 'from-blue-500 to-purple-600',
      path: '/worlds',
      status: '3/6 مكتملة',
      statusColor: 'text-blue-400'
    },
    {
      id: 'time-rush',
      title: 'سباق الزمن',
      description: '60 ثانية من الإثارة',
      icon: Clock,
      color: 'from-yellow-500 to-orange-600',
      path: '/time-rush',
      status: 'أفضل نتيجة: 850',
      statusColor: 'text-yellow-400'
    },
    {
      id: 'friend-challenge',
      title: 'تحدي الأصدقاء',
      description: 'العب مع الآخرين',
      icon: Users,
      color: 'from-green-500 to-teal-600',
      path: '/friend-challenge',
      status: '5 أصدقاء متصلين',
      statusColor: 'text-green-400'
    },
    {
      id: 'daily-riddle',
      title: 'لغز اليوم',
      description: 'تحدي عقلك',
      icon: HelpCircle,
      color: 'from-purple-500 to-pink-600',
      path: '/daily-riddle',
      status: 'لم يُحل بعد',
      statusColor: 'text-purple-400'
    },
    {
      id: 'leaderboard',
      title: 'لوحة المتصدرين',
      description: 'تنافس مع الأفضل',
      icon: Trophy,
      color: 'from-amber-500 to-yellow-600',
      path: '/leaderboard',
      status: 'المركز #42',
      statusColor: 'text-amber-400'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'achievement',
      title: 'حصلت على إنجاز جديد',
      description: 'سلسلة انتصارات - 7 أيام',
      time: 'منذ ساعتين',
      icon: Award,
      color: 'text-yellow-400'
    },
    {
      id: 2,
      type: 'game',
      title: 'أكملت تحدي اليوم',
      description: 'حصلت على 150 نقطة',
      time: 'منذ 4 ساعات',
      icon: Star,
      color: 'text-blue-400'
    },
    {
      id: 3,
      type: 'level',
      title: 'وصلت للمستوى 12',
      description: 'مبروك! استمر في التقدم',
      time: 'أمس',
      icon: TrendingUp,
      color: 'text-green-400'
    },
    {
      id: 4,
      type: 'world',
      title: 'أكملت عالم العلوم',
      description: 'دقة 85% - ممتاز!',
      time: 'منذ يومين',
      icon: Globe,
      color: 'text-purple-400'
    }
  ];

  const quickStats = [
    {
      label: 'النقاط الإجمالية',
      value: formatNumber(user.totalScore),
      icon: Star,
      color: 'text-yellow-400',
      change: '+150',
      changeColor: 'text-green-400'
    },
    {
      label: 'الألعاب المكتملة',
      value: formatNumber(user.gamesPlayed),
      icon: Play,
      color: 'text-blue-400',
      change: '+3',
      changeColor: 'text-green-400'
    },
    {
      label: 'دقة الإجابات',
      value: '85%',
      icon: Target,
      color: 'text-green-400',
      change: '+2%',
      changeColor: 'text-green-400'
    },
    {
      label: 'سلسلة الانتصارات',
      value: `${user.streakDays} أيام`,
      icon: Fire,
      color: 'text-red-400',
      change: '+1',
      changeColor: 'text-green-400'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-2xl p-6 border border-white/10"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 font-arabic">
              مرحباً، {user.username}! 👋
            </h1>
            <p className="text-white/70 font-arabic">
              مستعد لتحدي جديد؟ لديك {formatNumber(user.xp)} نقطة خبرة
            </p>
          </div>

          <div className="text-center">
            <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mb-2">
              <span className="text-2xl font-bold text-white">{currentLevel}</span>
            </div>
            <p className="text-xs text-white/60 font-arabic">المستوى</p>
          </div>
        </div>

        {/* XP Progress Bar */}
        <div className="mt-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-white/70 font-arabic">التقدم للمستوى التالي</span>
            <span className="text-sm text-white/70 font-arabic">
              {formatNumber(user.xp % 1000)} / {formatNumber(1000)} XP
            </span>
          </div>
          <div className="w-full h-3 bg-white/20 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-primary-400 to-secondary-400"
              initial={{ width: 0 }}
              animate={{ width: `${xpProgress}%` }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </div>
        </div>
      </motion.div>

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-2 lg:grid-cols-4 gap-4"
      >
        {quickStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 + index * 0.1 }}
              className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10 hover:border-white/20 transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-2">
                <Icon className={`w-5 h-5 ${stat.color}`} />
                <span className={`text-xs ${stat.changeColor} font-semibold`}>
                  {stat.change}
                </span>
              </div>
              <p className="text-2xl font-bold text-white mb-1">{stat.value}</p>
              <p className="text-xs text-white/60 font-arabic">{stat.label}</p>
            </motion.div>
          );
        })}
      </motion.div>

      {/* Game Modes Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <h2 className="text-2xl font-bold text-white mb-6 font-arabic">أوضاع اللعب</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {gameModesCards.map((mode, index) => {
            const Icon = mode.icon;
            return (
              <motion.div
                key={mode.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                whileHover={{ scale: 1.02, y: -5 }}
                className="group"
              >
                <Link
                  to={mode.path}
                  className="block bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300"
                >
                  <div className={`w-12 h-12 bg-gradient-to-br ${mode.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>

                  <h3 className="text-lg font-bold text-white mb-2 font-arabic">
                    {mode.title}
                  </h3>

                  <p className="text-white/70 text-sm mb-4 font-arabic">
                    {mode.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className={`text-xs ${mode.statusColor} font-arabic`}>
                      {mode.status}
                    </span>
                    <ChevronLeft className="w-4 h-4 text-white/50 group-hover:text-white group-hover:translate-x-1 transition-all" />
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </div>
      </motion.div>

      {/* Recent Activity & Daily Challenge */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
          className="lg:col-span-2"
        >
          <h2 className="text-2xl font-bold text-white mb-6 font-arabic">النشاط الأخير</h2>
          <div className="bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 overflow-hidden">
            {recentActivities.map((activity, index) => {
              const Icon = activity.icon;
              return (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  className="p-4 border-b border-white/10 last:border-b-0 hover:bg-white/5 transition-colors"
                >
                  <div className="flex items-start space-x-4 rtl:space-x-reverse">
                    <div className={`p-2 rounded-lg bg-white/10`}>
                      <Icon className={`w-4 h-4 ${activity.color}`} />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-semibold text-sm font-arabic">
                        {activity.title}
                      </h4>
                      <p className="text-white/70 text-xs mt-1 font-arabic">
                        {activity.description}
                      </p>
                      <p className="text-white/50 text-xs mt-2 font-arabic">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Daily Challenge Status */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <h2 className="text-2xl font-bold text-white mb-6 font-arabic">تحدي اليوم</h2>
          <div className="bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-xl p-6 border border-orange-500/30">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2 font-arabic">
                سؤال اليوم
              </h3>
              <p className="text-white/70 text-sm font-arabic">
                تحدي جديد في انتظارك!
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-white/70 font-arabic">المكافأة</span>
                <span className="text-sm font-bold text-orange-400">200 نقطة</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-white/70 font-arabic">الصعوبة</span>
                <span className="text-sm font-bold text-yellow-400">متوسط</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-white/70 font-arabic">الوقت المتبقي</span>
                <span className="text-sm font-bold text-red-400">18:42:15</span>
              </div>
            </div>

            <Link
              to="/daily-challenge"
              className="block w-full mt-6 py-3 bg-gradient-to-r from-orange-500 to-red-600 text-white font-bold rounded-lg text-center hover:from-orange-600 hover:to-red-700 transition-all duration-300 font-arabic"
            >
              ابدأ التحدي
            </Link>
          </div>

          {/* World Progress Preview */}
          <div className="mt-6 bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
            <h3 className="text-lg font-bold text-white mb-4 font-arabic">تقدم العوالم</h3>
            <div className="space-y-3">
              {[
                { name: 'التاريخ', progress: 100, color: 'bg-green-500' },
                { name: 'العلوم', progress: 85, color: 'bg-blue-500' },
                { name: 'الثقافة', progress: 60, color: 'bg-purple-500' },
                { name: 'السينما', progress: 30, color: 'bg-red-500' },
                { name: 'التكنولوجيا', progress: 0, color: 'bg-gray-500' },
                { name: 'الغموض', progress: 0, color: 'bg-gray-500' }
              ].map((world, index) => (
                <div key={world.name} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-white/70 font-arabic">{world.name}</span>
                    <span className="text-sm text-white/70">{world.progress}%</span>
                  </div>
                  <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
                    <motion.div
                      className={`h-full ${world.color}`}
                      initial={{ width: 0 }}
                      animate={{ width: `${world.progress}%` }}
                      transition={{ duration: 1, delay: 0.6 + index * 0.1 }}
                    />
                  </div>
                </div>
              ))}
            </div>

            <Link
              to="/worlds"
              className="block w-full mt-4 py-2 bg-white/10 text-white text-center rounded-lg hover:bg-white/20 transition-colors font-arabic text-sm"
            >
              عرض جميع العوالم
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default DashboardPage;
